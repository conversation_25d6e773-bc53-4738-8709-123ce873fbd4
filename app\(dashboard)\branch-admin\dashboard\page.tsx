"use client";

import { useState } from "react";
import {
  Users,
  TrendingUp,
  DollarSign,
  Clock,
  Activity,
  Target,
  UserCheck,
  Award,
  Repeat,
  UserPlus
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Legend,
  AreaChart,
  Area,
  ComposedChart
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { formatCurrency } from "@/lib/utils/currency";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

// Filter periods
type FilterPeriod = "7days" | "30days" | "90days" | "custom";

// Mock data generator based on filter period
const generateMockData = (period: FilterPeriod) => {
  const baseMultiplier = period === "7days" ? 0.3 : period === "30days" ? 1 : period === "90days" ? 2.5 : 1;

  return {
    // KPI Data
    kpis: {
      activeTellers: 5,
      dailyTransactions: Math.round(873 * baseMultiplier),
      dailyRevenue: Math.round(223450 * baseMultiplier),
      avgTransactionValue: Math.round(256 * (0.8 + baseMultiplier * 0.4)),
      newCustomers: Math.round(45 * baseMultiplier),
      returningCustomers: Math.round(128 * baseMultiplier),
      retentionRate: Math.round(87.5 + (baseMultiplier - 1) * 5),
    },

    // Transaction Volume Data (24-hour format)
    transactionVolumeData: [
      { date: period === "7days" ? "Jan 15" : period === "30days" ? "Week 1" : "Month 1",
        daily: Math.round(156 * baseMultiplier),
        weekly: Math.round(892 * baseMultiplier),
        monthly: Math.round(3420 * baseMultiplier) },
      { date: period === "7days" ? "Jan 16" : period === "30days" ? "Week 2" : "Month 2",
        daily: Math.round(142 * baseMultiplier),
        weekly: Math.round(898 * baseMultiplier),
        monthly: Math.round(3562 * baseMultiplier) },
      { date: period === "7days" ? "Jan 17" : period === "30days" ? "Week 3" : "Month 3",
        daily: Math.round(178 * baseMultiplier),
        weekly: Math.round(945 * baseMultiplier),
        monthly: Math.round(3740 * baseMultiplier) },
      { date: period === "7days" ? "Jan 18" : period === "30days" ? "Week 4" : "Month 4",
        daily: Math.round(165 * baseMultiplier),
        weekly: Math.round(967 * baseMultiplier),
        monthly: Math.round(3905 * baseMultiplier) },
      { date: period === "7days" ? "Jan 19" : period === "30days" ? "Jan" : "Month 5",
        daily: Math.round(189 * baseMultiplier),
        weekly: Math.round(1024 * baseMultiplier),
        monthly: Math.round(4094 * baseMultiplier) },
    ],

    // Daily transaction hours (12am to 11:59pm)
    dailyTransactionHours: [
      { hour: "12am", transactions: Math.round(8 * baseMultiplier) },
      { hour: "1am", transactions: Math.round(5 * baseMultiplier) },
      { hour: "2am", transactions: Math.round(3 * baseMultiplier) },
      { hour: "3am", transactions: Math.round(2 * baseMultiplier) },
      { hour: "4am", transactions: Math.round(1 * baseMultiplier) },
      { hour: "5am", transactions: Math.round(2 * baseMultiplier) },
      { hour: "6am", transactions: Math.round(8 * baseMultiplier) },
      { hour: "7am", transactions: Math.round(25 * baseMultiplier) },
      { hour: "8am", transactions: Math.round(45 * baseMultiplier) },
      { hour: "9am", transactions: Math.round(78 * baseMultiplier) },
      { hour: "10am", transactions: Math.round(95 * baseMultiplier) },
      { hour: "11am", transactions: Math.round(112 * baseMultiplier) },
      { hour: "12pm", transactions: Math.round(134 * baseMultiplier) },
      { hour: "1pm", transactions: Math.round(156 * baseMultiplier) },
      { hour: "2pm", transactions: Math.round(142 * baseMultiplier) },
      { hour: "3pm", transactions: Math.round(128 * baseMultiplier) },
      { hour: "4pm", transactions: Math.round(98 * baseMultiplier) },
      { hour: "5pm", transactions: Math.round(67 * baseMultiplier) },
      { hour: "6pm", transactions: Math.round(45 * baseMultiplier) },
      { hour: "7pm", transactions: Math.round(32 * baseMultiplier) },
      { hour: "8pm", transactions: Math.round(28 * baseMultiplier) },
      { hour: "9pm", transactions: Math.round(18 * baseMultiplier) },
      { hour: "10pm", transactions: Math.round(12 * baseMultiplier) },
      { hour: "11pm", transactions: Math.round(10 * baseMultiplier) },
    ],

    // Average Transaction Value Data
    avgTransactionValueData: [
      { period: period === "7days" ? "Day 1" : period === "30days" ? "Week 1" : "Month 1",
        value: Math.round(234 * (0.8 + baseMultiplier * 0.4)) },
      { period: period === "7days" ? "Day 2" : period === "30days" ? "Week 2" : "Month 2",
        value: Math.round(245 * (0.8 + baseMultiplier * 0.4)) },
      { period: period === "7days" ? "Day 3" : period === "30days" ? "Week 3" : "Month 3",
        value: Math.round(238 * (0.8 + baseMultiplier * 0.4)) },
      { period: period === "7days" ? "Day 4" : period === "30days" ? "Week 4" : "Month 4",
        value: Math.round(252 * (0.8 + baseMultiplier * 0.4)) },
      { period: period === "7days" ? "Day 5" : period === "30days" ? "Current" : "Month 5",
        value: Math.round(267 * (0.8 + baseMultiplier * 0.4)) },
    ],

    // New vs Returning Customers
    newVsReturningData: [
      { type: "New Customers", count: Math.round(45 * baseMultiplier),
        revenue: Math.round(67500 * baseMultiplier), percentage: 26 },
      { type: "Returning Customers", count: Math.round(128 * baseMultiplier),
        revenue: Math.round(156000 * baseMultiplier), percentage: 74 },
    ],

    // Customer return frequency trends
    returnFrequencyTrends: [
      { period: period === "7days" ? "Day 1" : period === "30days" ? "Week 1" : "Month 1",
        daily: Math.round(12 * baseMultiplier),
        weekly: Math.round(35 * baseMultiplier),
        monthly: Math.round(28 * baseMultiplier) },
      { period: period === "7days" ? "Day 2" : period === "30days" ? "Week 2" : "Month 2",
        daily: Math.round(15 * baseMultiplier),
        weekly: Math.round(38 * baseMultiplier),
        monthly: Math.round(25 * baseMultiplier) },
      { period: period === "7days" ? "Day 3" : period === "30days" ? "Week 3" : "Month 3",
        daily: Math.round(18 * baseMultiplier),
        weekly: Math.round(42 * baseMultiplier),
        monthly: Math.round(22 * baseMultiplier) },
      { period: period === "7days" ? "Day 4" : period === "30days" ? "Week 4" : "Month 4",
        daily: Math.round(22 * baseMultiplier),
        weekly: Math.round(45 * baseMultiplier),
        monthly: Math.round(20 * baseMultiplier) },
      { period: period === "7days" ? "Day 5" : period === "30days" ? "Current" : "Month 5",
        daily: Math.round(25 * baseMultiplier),
        weekly: Math.round(42 * baseMultiplier),
        monthly: Math.round(22 * baseMultiplier) },
    ],

    // Top Repeat Customers
    topRepeatCustomers: [
      { name: "David Wilson", phone: "+233 24 123 4567",
        transactions: Math.round(18 * baseMultiplier),
        totalSpend: Math.round(4250 * baseMultiplier),
        frequency: period === "7days" ? "Daily" : period === "30days" ? "Weekly" : "Monthly" },
      { name: "Emma Thompson", phone: "+233 24 234 5678",
        transactions: Math.round(15 * baseMultiplier),
        totalSpend: Math.round(3890 * baseMultiplier),
        frequency: period === "7days" ? "Every 2 days" : period === "30days" ? "Bi-weekly" : "Bi-monthly" },
      { name: "Robert Chen", phone: "+233 24 345 6789",
        transactions: Math.round(12 * baseMultiplier),
        totalSpend: Math.round(3650 * baseMultiplier),
        frequency: period === "7days" ? "Every 3 days" : period === "30days" ? "Weekly" : "Monthly" },
    ],

    // Teller Performance Data (removed efficiency and performance scores)
    tellerPerformanceData: [
      { name: "John Smith", transactions: Math.round(189 * baseMultiplier),
        avgValue: Math.round(278 * (0.8 + baseMultiplier * 0.4)),
        customersSaved: Math.round(45 * baseMultiplier) },
      { name: "Sarah Johnson", transactions: Math.round(176 * baseMultiplier),
        avgValue: Math.round(312 * (0.8 + baseMultiplier * 0.4)),
        customersSaved: Math.round(38 * baseMultiplier) },
      { name: "Mike Brown", transactions: Math.round(162 * baseMultiplier),
        avgValue: Math.round(258 * (0.8 + baseMultiplier * 0.4)),
        customersSaved: Math.round(41 * baseMultiplier) },
      { name: "Lisa Davis", transactions: Math.round(195 * baseMultiplier),
        avgValue: Math.round(273 * (0.8 + baseMultiplier * 0.4)),
        customersSaved: Math.round(52 * baseMultiplier) },
      { name: "Tom Wilson", transactions: Math.round(151 * baseMultiplier),
        avgValue: Math.round(245 * (0.8 + baseMultiplier * 0.4)),
        customersSaved: Math.round(29 * baseMultiplier) },
    ],
  };
};

export default function BranchAdminDashboard() {
  const { user } = useAuth();
  const [selectedPeriod] = useState<FilterPeriod>("30days");

  if (!user || user.role !== 'branch_admin') {
    return <div>Access denied. Branch Admin role required.</div>;
  }

  // Generate data based on selected filter period
  const data = generateMockData(selectedPeriod);

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Branch Admin Dashboard</h2>
        <p className="text-muted-foreground">
          Comprehensive branch analytics, teller oversight, and customer insights
        </p>
      </div>

      <FilterBar />

      {/* Enhanced KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5">
        <KpiCard
          title="Active Tellers"
          value={data.kpis.activeTellers.toString()}
          icon={Users}
          trend={{ value: 0, isPositive: true }}
        />
        <KpiCard
          title="Daily Transactions"
          value={data.kpis.dailyTransactions.toString()}
          icon={Activity}
          trend={{ value: 12.5, isPositive: true }}
        />
        <KpiCard
          title="Daily Revenue"
          value={formatCurrency(data.kpis.dailyRevenue)}
          icon={DollarSign}
          trend={{ value: 8.7, isPositive: true }}
        />
        <KpiCard
          title="Avg. Transaction Value"
          value={formatCurrency(data.kpis.avgTransactionValue)}
          icon={Target}
          trend={{ value: 6.0, isPositive: true }}
        />
        <KpiCard
          title="Retention Rate"
          value={`${data.kpis.retentionRate}%`}
          icon={UserCheck}
          trend={{ value: 5.2, isPositive: true }}
        />
      </div>

      {/* Essential Overview Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Daily Transaction Overview">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data.newVsReturningData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="type" />
              <YAxis />
              <Tooltip formatter={(value, name) => {
                if (name === "revenue") return [formatCurrency(Number(value)), "Revenue"];
                return [value, name];
              }} />
              <Legend />
              <Bar dataKey="count" fill={COLORS[0]} name="Customer Count" />
              <Bar dataKey="revenue" fill={COLORS[1]} name="Revenue" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Branch Performance Summary">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data.newVsReturningData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ type, percentage }) => `${type}: ${percentage}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="percentage"
              >
                {data.newVsReturningData.map((_, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Main Dashboard Overview */}
      <div className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Transaction Volume Trends */}
            <ChartContainer title="Transaction Volume Trends">
              <ResponsiveContainer width="100%" height="100%">
                <ComposedChart data={data.transactionVolumeData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="daily" fill={COLORS[0]} name="Daily Volume" />
                  <Line type="monotone" dataKey="weekly" stroke={COLORS[1]} strokeWidth={2} name="Weekly Trend" />
                </ComposedChart>
              </ResponsiveContainer>
            </ChartContainer>

            {/* Daily Transactions, Total Transactions */}
            <ChartContainer title="Daily Transactions, Total Transactions">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={data.dailyTransactionHours}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="transactions" fill={COLORS[0]} name="Transactions" />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </div>

          {/* Average Transaction Value Analysis */}
          <div className="grid gap-6 md:grid-cols-2">
            <ChartContainer title="Average Transaction Value Trends">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={data.avgTransactionValueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Avg Value"]} />
                  <Line type="monotone" dataKey="value" stroke={COLORS[0]} strokeWidth={3} dot={{ r: 6 }} />
                </LineChart>
              </ResponsiveContainer>
            </ChartContainer>

            <ChartContainer title="Top Repeat Customers">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={data.topRepeatCustomers}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value, name) => {
                    if (name === "totalSpend") return [formatCurrency(Number(value)), "Total Spend"];
                    return [value, name];
                  }} />
                  <Legend />
                  <Bar dataKey="transactions" fill={COLORS[0]} name="Transactions" />
                  <Bar dataKey="totalSpend" fill={COLORS[1]} name="Total Spend" />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </div>

        {/* Additional Overview Charts */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Teller Performance Overview */}
          <ChartContainer title="Teller Performance Overview">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={data.tellerPerformanceData.slice(0, 3)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="transactions" fill={COLORS[0]} name="Transactions" />
                <Bar dataKey="avgValue" fill={COLORS[1]} name="Avg Value" />
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>

        </div>

        {/* Quick Insights Cards */}
        <div className="grid gap-6 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Peak Hour</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1pm</div>
              <p className="text-xs text-muted-foreground">
                {Math.max(...data.dailyTransactionHours.map((h: any) => h.transactions))} transactions
              </p>
              <Progress value={75} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Top Teller</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {data.tellerPerformanceData.reduce((prev, current) =>
                  (prev.transactions > current.transactions) ? prev : current
                ).name}
              </div>
              <p className="text-xs text-muted-foreground">
                Most transactions
              </p>
              <Progress value={85} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">New Customers</CardTitle>
              <UserPlus className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.kpis.newCustomers}</div>
              <p className="text-xs text-muted-foreground">
                This period
              </p>
              <Progress value={65} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Returning Customers</CardTitle>
              <Repeat className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.kpis.returningCustomers}</div>
              <p className="text-xs text-muted-foreground">
                {data.kpis.retentionRate}% retention rate
              </p>
              <Progress value={data.kpis.retentionRate} className="mt-2" />
            </CardContent>
          </Card>
        </div>

        
      </div>
    </div>
  );
}
