"use client";

import { useState } from "react";
import {
  <PERSON>,
  <PERSON>,
  T<PERSON><PERSON>U<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>riangle,
  Activity
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  LineChart,
  Line,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"];

// Dummy data for branch customer segmentation
const dummyBranchSegmentationData = [
  { name: "High Value", value: 25, color: COLORS[0] },
  { name: "Mid Value", value: 65, color: COLORS[1] },
  { name: "Low Value", value: 130, color: COLORS[2] }
];

const dummyBranchSegmentTrends = [
  { month: "Jan", highValue: 22, midValue: 60, lowValue: 125 },
  { month: "Feb", highValue: 25, midValue: 65, lowValue: 130 },
  { month: "Mar", highValue: 23, midValue: 62, lowValue: 128 },
  { month: "Apr", highValue: 28, midValue: 68, lowValue: 135 },
  { month: "May", highValue: 30, midValue: 72, lowValue: 142 },
  { month: "Jun", highValue: 28, midValue: 70, lowValue: 138 }
];

const dummyBranchSegmentDetails = [
  { 
    segment: "High Value", 
    customers: 25, 
    avgTransaction: 420, 
    totalSpend: 10500, 
    frequency: 7.8,
    branchLoyalty: 92,
    color: COLORS[0]
  },
  { 
    segment: "Mid Value", 
    customers: 65, 
    avgTransaction: 180, 
    totalSpend: 11700, 
    frequency: 4.5,
    branchLoyalty: 78,
    color: COLORS[1]
  },
  { 
    segment: "Low Value", 
    customers: 130, 
    avgTransaction: 85, 
    totalSpend: 11050, 
    frequency: 2.2,
    branchLoyalty: 55,
    color: COLORS[2]
  }
];

const dummyBranchValueDistribution = [
  { range: "₵0-100", count: 85, percentage: 39 },
  { range: "₵101-300", count: 70, percentage: 32 },
  { range: "₵301-600", count: 45, percentage: 20 },
  { range: "₵601-1000", count: 15, percentage: 7 },
  { range: "₵1000+", count: 5, percentage: 2 }
];

const dummyBranchCustomerLifetime = [
  { segment: "High Value", avgLifetime: 18, avgSpend: 1850, retention: 92, branchLoyalty: 95 },
  { segment: "Mid Value", avgLifetime: 14, avgSpend: 680, retention: 78, branchLoyalty: 82 },
  { segment: "Low Value", avgLifetime: 8, avgSpend: 180, retention: 55, branchLoyalty: 60 }
];

export default function BranchCustomerSegmentationPage() {
  const { user } = useAuth();

  if (!user || user.role !== 'branch_admin') {
    return <div>Access denied. Branch Admin role required.</div>;
  }

  // Calculate totals for KPIs
  const totalBranchCustomers = dummyBranchSegmentationData.reduce((sum, item) => sum + item.value, 0);
  const highValueCustomers = dummyBranchSegmentationData.find(item => item.name === "High Value")?.value || 0;
  const midValueCustomers = dummyBranchSegmentationData.find(item => item.name === "Mid Value")?.value || 0;
  const lowValueCustomers = dummyBranchSegmentationData.find(item => item.name === "Low Value")?.value || 0;

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Customer Segmentation</h2>
        <p className="text-muted-foreground">
          Segment customers based on their transaction volume or value at this branch
        </p>
      </div>

      <FilterBar
      />

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Total Branch Customers"
          value={totalBranchCustomers.toString()}
          icon={Users}
          trend={{ value: 12.5, isPositive: true }}
        />
        <KpiCard
          title="High-Value Customers"
          value={highValueCustomers.toString()}
          icon={Star}
          trend={{ value: 18.3, isPositive: true }}
        />
        <KpiCard
          title="Mid-Value Customers"
          value={midValueCustomers.toString()}
          icon={TrendingUp}
          trend={{ value: 8.7, isPositive: true }}
        />
        <KpiCard
          title="Low-Value Customers"
          value={lowValueCustomers.toString()}
          icon={PieChart}
          trend={{ value: 5.2, isPositive: false }}
        />
      </div>

      {/* Branch Segmentation Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Branch Customer Segmentation">
          <ResponsiveContainer width="100%" height="100%">
            <RechartsPieChart>
              <Pie
                data={dummyBranchSegmentationData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, value, percent }) => `${name}: ${value} (${(percent * 100).toFixed(1)}%)`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {dummyBranchSegmentationData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip formatter={(value, name) => [value, `${name} Customers`]} />
              <Legend />
            </RechartsPieChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Branch Segment Trends">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={dummyBranchSegmentTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="highValue" stroke={COLORS[0]} strokeWidth={3} name="High Value" />
              <Line type="monotone" dataKey="midValue" stroke={COLORS[1]} strokeWidth={3} name="Mid Value" />
              <Line type="monotone" dataKey="lowValue" stroke={COLORS[2]} strokeWidth={3} name="Low Value" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Value Distribution & Customer Lifetime */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Branch Transaction Value Distribution">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyBranchValueDistribution}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="range" />
              <YAxis />
              <Tooltip formatter={(value, name) => [value, name === "count" ? "Customers" : "Percentage"]} />
              <Bar dataKey="count" fill={COLORS[3]} name="Customer Count" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Branch Customer Lifetime Value">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyBranchCustomerLifetime}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="segment" />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "avgSpend" ? formatCurrency(Number(value)) : 
                name === "retention" || name === "branchLoyalty" ? `${value}%` : 
                `${value} months`,
                name === "avgSpend" ? "Avg Spend" : 
                name === "retention" ? "Retention Rate" : 
                name === "branchLoyalty" ? "Branch Loyalty" :
                "Avg Lifetime"
              ]} />
              <Bar dataKey="avgSpend" fill={COLORS[4]} name="Avg Spend" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Branch Segment Performance */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Branch Segment Performance Overview</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {dummyBranchSegmentDetails.map((segment, index) => (
                <div key={segment.segment} className="p-4 border rounded-lg">
                  <div className="flex items-center space-x-2 mb-3">
                    <div 
                      className="w-4 h-4 rounded-full" 
                      style={{ backgroundColor: segment.color }}
                    ></div>
                    <h3 className="font-semibold">{segment.segment}</h3>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Customers:</span>
                      <span className="font-medium">{segment.customers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Avg Transaction:</span>
                      <span className="font-medium">{formatCurrency(segment.avgTransaction)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Total Spend:</span>
                      <span className="font-medium">{formatCurrency(segment.totalSpend)}</span>
                    </div>
                  
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>


    </div>
  );
}
