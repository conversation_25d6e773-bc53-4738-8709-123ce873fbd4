"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Calendar as CalendarIcon, ChevronDown } from "lucide-react";
import { format } from "date-fns";

export type FilterPeriod = '7days' | '30days' | '90days' | 'custom';

interface MerchantFilterBarProps {
  selectedPeriod?: FilterPeriod;
  onPeriodChange?: (period: FilterPeriod) => void;
  customStartDate?: Date;
  customEndDate?: Date;
  onCustomDateChange?: (startDate: Date, endDate: Date) => void;
  isLoading?: boolean;
}

export function MerchantFilterBar({
  selectedPeriod = '30days',
  onPeriodChange,
  customStartDate,
  customEndDate,
  onCustomDateChange,
  isLoading = false
}: MerchantFilterBarProps) {
  const [isStartDateOpen, setIsStartDateOpen] = useState(false);
  const [isEndDateOpen, setIsEndDateOpen] = useState(false);

  const periodLabels = {
    '7days': 'Last 7 days',
    '30days': 'Last 30 days',
    '90days': 'Last 90 days',
    'custom': 'Custom range'
  };

  const handlePeriodSelect = (period: FilterPeriod) => {
    onPeriodChange?.(period);
  };

  const handleStartDateSelect = (date: Date | undefined) => {
    if (date && customEndDate) {
      onCustomDateChange?.(date, customEndDate);
    } else if (date) {
      // If only start date is selected, set end date to today
      onCustomDateChange?.(date, new Date());
    }
    setIsStartDateOpen(false);
  };

  const handleEndDateSelect = (date: Date | undefined) => {
    if (date && customStartDate) {
      onCustomDateChange?.(customStartDate, date);
    } else if (date) {
      // If only end date is selected, set start date to 30 days ago
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);
      onCustomDateChange?.(startDate, date);
    }
    setIsEndDateOpen(false);
  };

  const formatDateRange = () => {
    if (selectedPeriod === 'custom' && customStartDate && customEndDate) {
      return `${format(customStartDate, 'MMM dd, yyyy')} - ${format(customEndDate, 'MMM dd, yyyy')}`;
    }
    return 'Pick a date range';
  };

  return (
    <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="flex items-center gap-2">
        {/* Time Period Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-8 w-[140px] justify-between bg-blue-600 hover:bg-blue-600 hover:text-white text-white"
              disabled={isLoading}
            >
              {periodLabels[selectedPeriod]}
              <ChevronDown className="ml-2 h-3.5 w-3.5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            <DropdownMenuItem onClick={() => handlePeriodSelect('7days')}>
              Last 7 days
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handlePeriodSelect('30days')}>
              Last 30 days
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handlePeriodSelect('90days')}>
              Last 90 days
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handlePeriodSelect('custom')}>
              Custom range
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Custom Date Range Pickers */}
        {selectedPeriod === 'custom' && (
          <div className="flex items-center gap-2">
            {/* Start Date Picker */}
            <Popover open={isStartDateOpen} onOpenChange={setIsStartDateOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-[140px] justify-start"
                  disabled={isLoading}
                >
                  <CalendarIcon className="mr-2 h-3.5 w-3.5" />
                  {customStartDate ? format(customStartDate, 'MMM dd, yyyy') : 'Start date'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 z-[100]" align="start" side="bottom">
                <Calendar
                  mode="single"
                  selected={customStartDate}
                  onSelect={handleStartDateSelect}
                  disabled={(date) => {
                    const today = new Date();
                    today.setHours(23, 59, 59, 999); // End of today
                    return date > today || (customEndDate ? date > customEndDate : false);
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>

            {/* End Date Picker */}
            <Popover open={isEndDateOpen} onOpenChange={setIsEndDateOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-[140px] justify-start"
                  disabled={isLoading}
                >
                  <CalendarIcon className="mr-2 h-3.5 w-3.5" />
                  {customEndDate ? format(customEndDate, 'MMM dd, yyyy') : 'End date'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 z-[100]" align="start" side="bottom">
                <Calendar
                  mode="single"
                  selected={customEndDate}
                  onSelect={handleEndDateSelect}
                  disabled={(date) => {
                    const today = new Date();
                    today.setHours(23, 59, 59, 999); // End of today
                    return date > today || (customStartDate ? date < customStartDate : false);
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        )}
      </div>

      {/* Loading indicator */}
      {isLoading && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
          Loading data...
        </div>
      )}
    </div>
  );
}

// Helper function to convert FilterPeriod to API parameters
export function getApiParamsFromFilter(
  period: FilterPeriod,
  customStartDate?: Date,
  customEndDate?: Date
): { rangeDays?: number; startDate?: string; endDate?: string } {
  switch (period) {
    case '7days':
      return { rangeDays: 7 };
    case '30days':
      return { rangeDays: 30 };
    case '90days':
      return { rangeDays: 90 };
    case 'custom':
      if (customStartDate && customEndDate) {
        // Format dates for API (MM/DD/YYYY with URL encoding)
        const formatDate = (date: Date): string => {
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const year = date.getFullYear();
          return `${month}%2F${day}%2F${year}`;
        };

        return {
          startDate: formatDate(customStartDate),
          endDate: formatDate(customEndDate)
        };
      }
      return { rangeDays: 30 }; // fallback
    default:
      return { rangeDays: 30 };
  }
}
