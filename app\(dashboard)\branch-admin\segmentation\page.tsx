"use client";

import { useState } from "react";
import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Users,
  TrendingUp,
  DollarSign,
  Target,
  Star,
  UserCheck,
  Activity
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { formatCurrency } from "@/lib/utils/currency";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

// Filter periods
type FilterPeriod = "7days" | "30days" | "90days" | "custom";

// Mock data generator for customer segmentation
const generateSegmentationData = (period: FilterPeriod) => {
  const baseMultiplier = period === "7days" ? 0.3 : period === "30days" ? 1 : period === "90days" ? 2.5 : 1;

  return {
    // KPI Data
    kpis: {
      totalCustomers: Math.round(566 * baseMultiplier),
      activeSegments: 5,
      highValueCustomers: Math.round(89 * baseMultiplier),
      avgSegmentValue: 2450,
    },

    // Customer Segments by Spending Level (3 levels: High, Mid, Low)
    spendingSegments: [
      {
        segment: "High",
        description: "₵2000+ per month",
        count: Math.round(89 * baseMultiplier),
        percentage: 15.7,
        avgSpend: 5350,
        avgTransactionValue: 385,
        retentionRate: 91,
        frequency: "Weekly"
      },
      {
        segment: "Mid",
        description: "₵500-₵1999 per month",
        count: Math.round(203 * baseMultiplier),
        percentage: 35.9,
        avgSpend: 1200,
        avgTransactionValue: 180,
        retentionRate: 72,
        frequency: "Bi-weekly"
      },
      {
        segment: "Low",
        description: "Under ₵500 per month",
        count: Math.round(274 * baseMultiplier),
        percentage: 48.4,
        avgSpend: 172,
        avgTransactionValue: 70,
        retentionRate: 50,
        frequency: "Monthly"
      }
    ],

    // Customer Segments by Purchase Frequency (3 levels: High, Mid, Low)
    frequencySegments: [
      {
        segment: "High",
        description: "2+ times per week",
        count: Math.round(179 * baseMultiplier),
        percentage: 31.7,
        avgSpend: 3500,
        transactions: Math.round(245 * baseMultiplier),
      },
      {
        segment: "Mid",
        description: "2-4 times per month",
        count: Math.round(245 * baseMultiplier),
        percentage: 43.3,
        avgSpend: 1200,
        transactions: Math.round(89 * baseMultiplier),
      },
      {
        segment: "Low",
        description: "Once per month or less",
        count: Math.round(142 * baseMultiplier),
        percentage: 25.0,
        avgSpend: 450,
        transactions: Math.round(34 * baseMultiplier),
      }
    ],

    // Segment Performance Radar Data (3 levels: High, Mid, Low)
    segmentRadarData: [
      {
        segment: "High",
        spending: 85,
        frequency: 80,
        retention: 91,
        satisfaction: 88,
        growth: 82,
      },
      {
        segment: "Mid",
        spending: 55,
        frequency: 50,
        retention: 72,
        satisfaction: 75,
        growth: 65,
      },
      {
        segment: "Low",
        spending: 25,
        frequency: 20,
        retention: 50,
        satisfaction: 60,
        growth: 35,
      }
    ],

    // Revenue by Segment (3 levels: High, Mid, Low)
    revenueBySegment: [
      { segment: "High", revenue: Math.round(476150 * baseMultiplier), percentage: 42.3 },
      { segment: "Mid", revenue: Math.round(243600 * baseMultiplier), percentage: 35.9 },
      { segment: "Low", revenue: Math.round(47080 * baseMultiplier), percentage: 21.8 }
    ],
  };
};

export default function CustomerSegmentationPage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>("30days");

  if (!user || user.role !== 'branch_admin') {
    return <div>Access denied. Branch Admin role required.</div>;
  }

  // Generate data based on selected filter period
  const data = generateSegmentationData(selectedPeriod);

  const getSegmentBadge = (segment: string) => {
    switch (segment) {
      case "High":
        return <Badge className="bg-green-600">{segment}</Badge>;
      case "Mid":
        return <Badge className="bg-blue-600">{segment}</Badge>;
      case "Low":
        return <Badge className="bg-yellow-600">{segment}</Badge>;
      default:
        return <Badge>{segment}</Badge>;
    }
  };

  return (
    <div className="w-full max-w-full overflow-hidden space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Customer Segmentation Analysis</h2>
        <p className="text-muted-foreground">
          Group customers based on behavior patterns for targeted marketing strategies
        </p>
      </div>

      <FilterBar />

      {/* Segmentation KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Total Customers"
          value={data.kpis.totalCustomers.toString()}
          icon={Users}
          trend={{ value: 8.2, isPositive: true }}
        />
        <KpiCard
          title="Active Segments"
          value={data.kpis.activeSegments.toString()}
          icon={PieChartIcon}
          trend={{ value: 0, isPositive: true }}
        />
        <KpiCard
          title="High Value Customers"
          value={data.kpis.highValueCustomers.toString()}
          icon={Star}
          trend={{ value: 12.5, isPositive: true }}
        />
        <KpiCard
          title="Avg. Segment Value"
          value={formatCurrency(data.kpis.avgSegmentValue)}
          icon={Target}
          trend={{ value: 6.8, isPositive: true }}
        />
      </div>

      {/* Segmentation Charts */}
      <div className="grid gap-6 md:grid-cols-2 max-w-full">
        {/* Spending Level Segments */}
        <ChartContainer title="Customer Segments by Spending Level">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data.spendingSegments}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ segment, percentage }) => `${segment}: ${percentage}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="percentage"
              >
                {data.spendingSegments.map((_, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>

        {/* Purchase Frequency Segments */}
        <ChartContainer title="Customer Segments by Purchase Frequency">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data.frequencySegments}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="segment" />
              <YAxis
                yAxisId="left"
                label={{ value: 'Customer Count', angle: -90, position: 'insideLeft' }}
              />
              <YAxis
                yAxisId="right"
                orientation="right"
                label={{ value: 'Average Spend (₵)', angle: 90, position: 'insideRight' }}
              />
              <Tooltip formatter={(value, name) => {
                if (name === "avgSpend") return [formatCurrency(Number(value)), "Avg Spend"];
                return [value, name];
              }} />
              <Legend />
              <Bar yAxisId="left" dataKey="count" fill={COLORS[0]} name="Customer Count" />
              <Bar yAxisId="right" dataKey="avgSpend" fill={COLORS[1]} name="Avg Spend" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Revenue Distribution */}
      <ChartContainer title="Revenue Distribution by Customer Segment">
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data.revenueBySegment}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="segment" />
            <YAxis
              label={{ value: 'Revenue (₵)', angle: -90, position: 'insideLeft' }}
            />
            <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Revenue"]} />
            <Bar dataKey="revenue" fill={COLORS[0]} name="Revenue" />
          </BarChart>
        </ResponsiveContainer>
      </ChartContainer>

      {/* Segment Performance Radar */}
      <ChartContainer title="Segment Performance Analysis">
        <ResponsiveContainer width="100%" height={400}>
          <RadarChart data={data.segmentRadarData}>
            <PolarGrid />
            <PolarAngleAxis dataKey="segment" />
            <PolarRadiusAxis angle={90} domain={[0, 100]} />
            <Radar name="Spending" dataKey="spending" stroke={COLORS[0]} fill={COLORS[0]} fillOpacity={0.1} />
            <Radar name="Frequency" dataKey="frequency" stroke={COLORS[1]} fill={COLORS[1]} fillOpacity={0.1} />
            <Radar name="Retention" dataKey="retention" stroke={COLORS[2]} fill={COLORS[2]} fillOpacity={0.1} />
            <Radar name="Satisfaction" dataKey="satisfaction" stroke={COLORS[3]} fill={COLORS[3]} fillOpacity={0.1} />
            <Tooltip />
            <Legend />
          </RadarChart>
        </ResponsiveContainer>
      </ChartContainer>

      {/* Detailed Segment Analysis Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <PieChartIcon className="h-5 w-5" />
            <span>Detailed Customer Segment Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            title="Customer Segments by Spending Level"
            columns={[
              {
                key: "segment",
                title: "Segment",
                render: (value) => getSegmentBadge(value)
              },
              { key: "description", title: "Description" },
              { key: "count", title: "Customers" },
              { key: "percentage", title: "% of Total" },
              { key: "avgSpend", title: "Avg. Spend" },
              { key: "avgTransactionValue", title: "Avg. Transaction" },
              { key: "retentionRate", title: "Retention %" },
              { key: "frequency", title: "Visit Frequency" },
            ]}
            data={data.spendingSegments.map(segment => ({
              ...segment,
              avgSpend: formatCurrency(segment.avgSpend),
              avgTransactionValue: formatCurrency(segment.avgTransactionValue),
              percentage: `${segment.percentage}%`,
              retentionRate: `${segment.retentionRate}%`
            }))}
          />
        </CardContent>
      </Card>
    </div>
  );
}
