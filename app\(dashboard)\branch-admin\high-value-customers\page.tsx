"use client";

import { useState } from "react";
import {
  Star,
  Users,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Activity,
  Target
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  Legend,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Composed<PERSON>hart
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";
import { Alert, AlertDescription } from "@/components/ui/alert";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"];

// Dummy data for branch-specific high-value customer analysis
const dummyBranchTopCustomers = [
  { customerId: "CUST-001", amount: 45000, transactions: 125, avgTransaction: 360, percentOfBranch: 15.2, lastVisit: "2024-01-15" },
  { customerId: "CUST-045", amount: 38000, transactions: 98, avgTransaction: 388, percentOfBranch: 12.8, lastVisit: "2024-01-14" },
  { customerId: "CUST-089", amount: 32000, transactions: 85, avgTransaction: 376, percentOfBranch: 10.8, lastVisit: "2024-01-13" },
  { customerId: "CUST-123", amount: 28000, transactions: 72, avgTransaction: 389, percentOfBranch: 9.5, lastVisit: "2024-01-12" },
  { customerId: "CUST-156", amount: 25000, transactions: 68, avgTransaction: 368, percentOfBranch: 8.4, lastVisit: "2024-01-11" },
  { customerId: "CUST-178", amount: 22000, transactions: 65, avgTransaction: 338, percentOfBranch: 7.4, lastVisit: "2024-01-10" },
  { customerId: "CUST-201", amount: 20000, transactions: 58, avgTransaction: 345, percentOfBranch: 6.8, lastVisit: "2024-01-09" },
  { customerId: "CUST-234", amount: 18000, transactions: 52, avgTransaction: 346, percentOfBranch: 6.1, lastVisit: "2024-01-08" }
];

const dummyBranchSpendingTrends = [
  { month: "Jan", topCustomersSpend: 185000, averageSpend: 85000, branchTotal: 295000 },
  { month: "Feb", topCustomersSpend: 195000, averageSpend: 92000, branchTotal: 315000 },
  { month: "Mar", topCustomersSpend: 188000, averageSpend: 88000, branchTotal: 305000 },
  { month: "Apr", topCustomersSpend: 205000, averageSpend: 95000, branchTotal: 325000 },
  { month: "May", topCustomersSpend: 220000, averageSpend: 102000, branchTotal: 345000 },
  { month: "Jun", topCustomersSpend: 215000, averageSpend: 98000, branchTotal: 335000 }
];

const dummyBranchFrequencyAnalysis = [
  { range: "High Frequency (15+ transactions)", customers: 25, avgSpend: 28000, percentOfRevenue: 42, color: COLORS[0] },
  { range: "Medium Frequency (8-14 transactions)", customers: 45, avgSpend: 15000, percentOfRevenue: 32, color: COLORS[1] },
  { range: "Low Frequency (3-7 transactions)", customers: 65, avgSpend: 8000, percentOfRevenue: 18, color: COLORS[2] },
  { range: "Occasional (1-2 transactions)", customers: 85, avgSpend: 3000, percentOfRevenue: 8, color: COLORS[3] }
];

const dummyCustomerGrowth = [
  { month: "Jan", newCustomers: 15, returningCustomers: 185, totalRevenue: 295000 },
  { month: "Feb", newCustomers: 22, returningCustomers: 195, totalRevenue: 315000 },
  { month: "Mar", newCustomers: 18, returningCustomers: 188, totalRevenue: 305000 },
  { month: "Apr", newCustomers: 28, returningCustomers: 205, totalRevenue: 325000 },
  { month: "May", newCustomers: 32, returningCustomers: 220, totalRevenue: 345000 },
  { month: "Jun", newCustomers: 25, returningCustomers: 215, totalRevenue: 335000 }
];

export default function BranchHighValueCustomersPage() {
  const { user } = useAuth();


  if (!user || user.role !== 'branch_admin') {
    return <div>Access denied. Branch Admin role required.</div>;
  }

  // Calculate KPIs from branch customer data
  const totalBranchCustomers = dummyBranchTopCustomers.length;
  const totalBranchSpend = dummyBranchTopCustomers.reduce((sum, customer) => sum + customer.amount, 0);
  const avgBranchSpend = totalBranchCustomers > 0 ? totalBranchSpend / totalBranchCustomers : 0;
  const topCustomerPercentage = 62.8; // Top customers' contribution to branch revenue

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">High-Value Customer Identification</h2>
        <p className="text-muted-foreground">
          Identify customers who frequently transact or spend significantly more than average in this branch
        </p>
      </div>

      <FilterBar
      />

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Branch Top Customers"
          value={totalBranchCustomers.toString()}
          icon={Star}
          trend={{ value: 18.3, isPositive: true }}
        />
        <KpiCard
          title="Revenue Contribution from Top Customers"
          value={`${topCustomerPercentage}%`}
          icon={Target}
          trend={{ value: 25.0, isPositive: true }}
        />
        <KpiCard
          title="Total Top Customer Spend"
          value={formatCurrency(totalBranchSpend)}
          icon={DollarSign}
          trend={{ value: 22.7, isPositive: true }}
        />
        <KpiCard
          title="Avg Top Customer Spend"
          value={formatCurrency(avgBranchSpend)}
          icon={TrendingUp}
          trend={{ value: 15.4, isPositive: true }}
        />
      </div>

      {/* Branch Customer Analysis Charts */}
      <div className="grid gap-6 md:grid-cols-1">
        <ChartContainer title="Branch Top Customer Ranking">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyBranchTopCustomers.slice(0, 8)}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="customerId"
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis
                label={{ value: 'Customer Spend (₵)', angle: -90, position: 'insideLeft' }}
              />
              <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Amount"]} />
              <Bar dataKey="amount" fill={COLORS[0]} name="Customer Spend" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>


      </div>


      {/* Branch Top Customers Table */}
      <div>
        <DataTable
          title="Branch Top Customers"
          columns={[
            { key: "customerId", title: "Customer ID" },
            { key: "amount", title: "Total Amount", render: (value) => formatCurrency(value) },
            { key: "transactions", title: "Transactions" },
            { key: "avgTransaction", title: "Avg Transaction", render: (value) => formatCurrency(value) },
            { key: "percentOfBranch", title: "% of Branch", render: (value) => `${value}%` },
            { key: "lastVisit", title: "Last Visit" },
          ]}
          data={dummyBranchTopCustomers}
        />
      </div>


    </div>
  );
}
