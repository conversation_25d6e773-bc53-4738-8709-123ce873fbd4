"use client";

import { useState } from "react";
import {
  Star,
  Users,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Activity,
  Crown,
  Target
} from "lucide-react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Legend,
  <PERSON>att<PERSON><PERSON>,
  <PERSON>att<PERSON>,
  Composed<PERSON>hart
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { MerchantFilterBar, FilterPeriod, getApiParamsFromFilter } from "@/components/dashboard/MerchantFilterBar";
import { useAuth } from "@/lib/auth/context";
import { useMerchantOverview } from "@/lib/hooks/use-merchant-admin";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"];

// Dummy data for enhanced high-value customer analysis
const dummyTopSpendersByBranch = [
  { branchName: "Main Branch", topSpender: "CUST-001", amount: 45000, transactions: 125, avgTransaction: 360, percentOfTotal: 12.5 },
  { branchName: "Downtown Branch", topSpender: "CUST-045", amount: 38000, transactions: 98, avgTransaction: 388, percentOfTotal: 10.6 },
  { branchName: "Suburban Branch", topSpender: "CUST-089", amount: 32000, transactions: 85, avgTransaction: 376, percentOfTotal: 8.9 },
  { branchName: "Mall Branch", topSpender: "CUST-123", amount: 28000, transactions: 72, avgTransaction: 389, percentOfTotal: 7.8 },
  { branchName: "Airport Branch", topSpender: "CUST-156", amount: 25000, transactions: 68, avgTransaction: 368, percentOfTotal: 6.9 }
];

const dummySpendingTrends = [
  { month: "Jan", topCustomersSpend: 285000, averageSpend: 180000, totalCustomers: 450 },
  { month: "Feb", topCustomersSpend: 310000, averageSpend: 195000, totalCustomers: 485 },
  { month: "Mar", topCustomersSpend: 295000, averageSpend: 188000, totalCustomers: 470 },
  { month: "Apr", topCustomersSpend: 325000, averageSpend: 205000, totalCustomers: 510 },
  { month: "May", topCustomersSpend: 340000, averageSpend: 220000, totalCustomers: 535 },
  { month: "Jun", topCustomersSpend: 355000, averageSpend: 235000, totalCustomers: 560 }
];

const dummyFrequencyAnalysis = [
  { range: "High Frequency", customers: 45, avgSpend: 35000, percentOfRevenue: 35, color: COLORS[0] },
  { range: "Medium Frequency", customers: 85, avgSpend: 18000, percentOfRevenue: 30, color: COLORS[1] },
  { range: "Low Frequency", customers: 120, avgSpend: 8000, percentOfRevenue: 20, color: COLORS[2] },
  { range: "Occasional", customers: 165, avgSpend: 3000, percentOfRevenue: 15, color: COLORS[3] }
];

const dummyFrequencyVsValue = [
  { customerId: "CUST-001", frequency: 25, totalSpend: 45000, avgTransaction: 1800, percentOfTotal: 12.5 },
  { customerId: "CUST-002", frequency: 22, totalSpend: 38000, avgTransaction: 1727, percentOfTotal: 10.6 },
  { customerId: "CUST-003", frequency: 20, totalSpend: 32000, avgTransaction: 1600, percentOfTotal: 8.9 },
  { customerId: "CUST-004", frequency: 18, totalSpend: 28000, avgTransaction: 1556, percentOfTotal: 7.8 },
  { customerId: "CUST-005", frequency: 16, totalSpend: 25000, avgTransaction: 1563, percentOfTotal: 6.9 },
  { customerId: "CUST-006", frequency: 15, totalSpend: 22000, avgTransaction: 1467, percentOfTotal: 6.1 },
  { customerId: "CUST-007", frequency: 14, totalSpend: 20000, avgTransaction: 1429, percentOfTotal: 5.6 },
  { customerId: "CUST-008", frequency: 12, totalSpend: 18000, avgTransaction: 1500, percentOfTotal: 5.0 },
  { customerId: "CUST-009", frequency: 10, totalSpend: 15000, avgTransaction: 1500, percentOfTotal: 4.2 },
  { customerId: "CUST-010", frequency: 8, totalSpend: 12000, avgTransaction: 1500, percentOfTotal: 3.3 }
];

// Utility functions for data transformation
const transformTopCustomersData = (topCustomers: any) => {
  if (!topCustomers?.data) return [];

  const totalAmount = topCustomers.data.reduce((sum: number, customer: any) => sum + customer.amount, 0);

  return topCustomers.data.map((customer: any, index: number) => ({
    id: customer.customer_id,
    customerId: customer.customer_id,
    amount: customer.amount,
    formattedAmount: formatCurrency(customer.amount),
    merchantId: customer.merchant_id,
    rank: index + 1,
    percentOfTotal: totalAmount > 0 ? Math.round((customer.amount / totalAmount) * 100 * 10) / 10 : 0
  }));
};

// Error and Loading Components
const ChartErrorState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartEmptyState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartLoadingState = () => (
  <div className="flex items-center justify-center h-[300px]">
    <div className="space-y-3 w-full">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
      <Skeleton className="h-4 w-2/3" />
      <Skeleton className="h-32 w-full" />
    </div>
  </div>
);

// Helper function to determine chart state
const getChartState = (isLoading: boolean, error: any, hasData: boolean, dataName: string) => {
  if (isLoading) return { type: 'loading' };
  if (error) {
    if (error.message?.includes('404') && error.message?.includes('No data after filtering')) {
      return { type: 'empty', message: `No data after filtering for the selected duration` };
    }
    return { type: 'error', message: `Failed to load ${dataName}` };
  }
  if (!hasData) return { type: 'empty', message: `No ${dataName} available for the selected duration` };
  return { type: 'data' };
};

export default function HighValueCustomersPage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>('30days');
  const [customStartDate, setCustomStartDate] = useState<Date>();
  const [customEndDate, setCustomEndDate] = useState<Date>();

  if (!user || user.role !== 'merchant_admin') {
    return <div>Access denied. Merchant Admin role required.</div>;
  }

  // Get API parameters based on filter selection
  const apiParams = getApiParamsFromFilter(selectedPeriod, customStartDate, customEndDate);

  // Fetch merchant overview data
  const {
    data: overviewData,
    isLoading,
    error
  } = useMerchantOverview({
    merchantId: user.id,
    topMode: 'amount',
    topLimit: 20, // Get more customers for high-value analysis
    ...apiParams
  });

  // Transform API data
  const topCustomersData = overviewData ? transformTopCustomersData(overviewData.top_customers) : [];

  // Calculate KPIs from top customers data
  const totalTopCustomers = topCustomersData.length;
  const totalTopCustomerSpend = topCustomersData.reduce((sum: number, customer: any) => sum + customer.amount, 0);
  const avgTopCustomerSpend = totalTopCustomers > 0 ? totalTopCustomerSpend / totalTopCustomers : 0;
  const topCustomerPercentage = totalTopCustomers > 0 ? Math.round((totalTopCustomers / 1000) * 100 * 10) / 10 : 0; // Assuming 1000 total customers

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">High-Value Customer Identification</h2>
        <p className="text-muted-foreground">
          Identify customers who frequently transact or spend significantly more than average across all branches
        </p>
      </div>

      <MerchantFilterBar
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
        customStartDate={customStartDate}
        customEndDate={customEndDate}
        onCustomDateChange={(start, end) => {
          setCustomStartDate(start);
          setCustomEndDate(end);
        }}
        isLoading={isLoading}
      />

      {/* Error Alert */}
      {error && !error.message?.includes('No data after filtering') && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load customer data: {error.message}. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      )}

      {/* No Data Alert */}
      {error && error.message?.includes('No data after filtering') && (
        <Alert>
          <Activity className="h-4 w-4" />
          <AlertDescription>
            No high-value customer data available after filtering for the selected time period. Try selecting a different date range.
          </AlertDescription>
        </Alert>
      )}

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Top Customers"
          value={isLoading ? "..." : error ? "0" : totalTopCustomers.toString()}
          icon={Star}
          trend={{ value: 18.3, isPositive: true }}
        />
        <KpiCard
          title="% from Top Customers"
          value={isLoading ? "..." : error ? "0%" : `${topCustomerPercentage}%`}
          icon={Target}
          trend={{ value: 25.0, isPositive: true }}
        />
        <KpiCard
          title="Total Top Customer Spend"
          value={isLoading ? "..." : error ? formatCurrency(0) : formatCurrency(totalTopCustomerSpend)}
          icon={DollarSign}
          trend={{ value: 22.7, isPositive: true }}
        />
        <KpiCard
          title="Avg Top Customer Spend"
          value={isLoading ? "..." : error ? formatCurrency(0) : formatCurrency(avgTopCustomerSpend)}
          icon={TrendingUp}
          trend={{ value: 15.4, isPositive: true }}
        />
      </div>

      {/* High-Value Customer Analysis Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Top Spenders by Branch">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyTopSpendersByBranch}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="branchName"
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis />
              <Tooltip formatter={(value) => [formatCurrency(Number(value)), "Amount"]} />
              <Bar dataKey="amount" fill={COLORS[0]} name="Top Spender Amount" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

         <ChartContainer title="Revenue by Frequency Segment">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyFrequencyAnalysis}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="range"
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
              />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "percentOfRevenue" ? `${value}%` : formatCurrency(Number(value)),
                name === "percentOfRevenue" ? "% of Revenue" : "Avg Spend"
              ]} />
              <Bar dataKey="percentOfRevenue" fill={COLORS[3]} name="% of Revenue" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

      </div>



      {/* Top Customers Table */}
      <div>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            <span className="ml-2">Loading customer data...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <div className={error.message?.includes('No data after filtering') ? "text-muted-foreground" : "text-red-600"}>
              {error.message?.includes('No data after filtering')
                ? "No customer data available for the selected time period"
                : `Error loading customer data: ${error.message}`
              }
            </div>
          </div>
        ) : (
          <DataTable
            title="Top Customers Ranking"
            columns={[
              { key: "rank", title: "Rank" },
              { key: "customerId", title: "Customer ID" },
              { key: "formattedAmount", title: "Total Amount" },
              { key: "percentOfTotal", title: "% of Total", render: (value) => `${value}%` },
            ]}
            data={topCustomersData}
          />
        )}
      </div>

      {/* Top Spenders by Branch Analysis */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Top Spenders Across All Branches</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Branch</th>
                    <th className="text-left p-2">Top Customer</th>
                    <th className="text-left p-2">Total Spend</th>
                    <th className="text-left p-2">Transactions</th>
                    <th className="text-left p-2">Avg Transaction</th>
                    <th className="text-left p-2">% of Total</th>
                  </tr>
                </thead>
                <tbody>
                  {dummyTopSpendersByBranch.map((branch, index) => (
                    <tr key={branch.branchName} className="border-b hover:bg-gray-50">
                      <td className="p-2 font-medium">{branch.branchName}</td>
                      <td className="p-2">{branch.topSpender}</td>
                      <td className="p-2 font-semibold">{formatCurrency(branch.amount)}</td>
                      <td className="p-2">{branch.transactions}</td>
                      <td className="p-2">{formatCurrency(branch.avgTransaction)}</td>
                      <td className="p-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          branch.percentOfTotal >= 10 ? 'bg-green-100 text-green-800' :
                          branch.percentOfTotal >= 8 ? 'bg-blue-100 text-blue-800' :
                          branch.percentOfTotal >= 6 ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {branch.percentOfTotal}%
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

   
    </div>
  );
}
