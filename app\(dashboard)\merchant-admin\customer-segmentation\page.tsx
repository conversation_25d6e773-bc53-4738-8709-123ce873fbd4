"use client";

import { useState } from "react";
import {
  <PERSON>,
  <PERSON>,
  TrendingU<PERSON>,
  <PERSON><PERSON><PERSON>,
  AlertTriangle,
  Activity
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  LineChart,
  Line,
  RadialBarChart,
  RadialBar
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { MerchantFilterBar, FilterPeriod, getApiParamsFromFilter } from "@/components/dashboard/MerchantFilterBar";
import { useAuth } from "@/lib/auth/context";
import { useMerchantOverview } from "@/lib/hooks/use-merchant-admin";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";
import { Al<PERSON>, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"];

// Dummy data for enhanced segmentation analysis
const dummySegmentTrends = [
  { month: "Jan", highValue: 45, midValue: 120, lowValue: 280 },
  { month: "Feb", highValue: 52, midValue: 135, lowValue: 295 },
  { month: "Mar", highValue: 48, midValue: 128, lowValue: 275 },
  { month: "Apr", highValue: 58, midValue: 142, lowValue: 310 },
  { month: "May", highValue: 65, midValue: 155, lowValue: 325 },
  { month: "Jun", highValue: 62, midValue: 148, lowValue: 315 }
];

const dummyValueDistribution = [
  { range: "₵0-50", count: 450, percentage: 35 },
  { range: "₵51-200", count: 380, percentage: 30 },
  { range: "₵201-500", count: 280, percentage: 22 },
  { range: "₵501-1000", count: 120, percentage: 9 },
  { range: "₵1000+", count: 50, percentage: 4 }
];

const dummyCustomerLifetime = [
  { segment: "High Value", avgLifetime: 24, avgSpend: 2500, retention: 85 },
  { segment: "Mid Value", avgLifetime: 18, avgSpend: 850, retention: 72 },
  { segment: "Low Value", avgLifetime: 12, avgSpend: 180, retention: 45 }
];

const dummySegmentDetails = [
  {
    segment: "High Value",
    customers: 65,
    avgTransaction: 450,
    totalSpend: 29250,
    frequency: 8.5,
    color: COLORS[0]
  },
  {
    segment: "Mid Value",
    customers: 155,
    avgTransaction: 125,
    totalSpend: 19375,
    frequency: 5.2,
    color: COLORS[1]
  },
  {
    segment: "Low Value",
    customers: 325,
    avgTransaction: 45,
    totalSpend: 14625,
    frequency: 2.8,
    color: COLORS[2]
  }
];

// Utility functions for data transformation
const transformSegmentationData = (segmentation: any) => {
  if (!segmentation?.data) return [];

  const { high_value, mid_value, low_value } = segmentation.data;

  return [
    { name: "High Value", value: high_value?.length || 0, color: COLORS[0] },
    { name: "Mid Value", value: mid_value?.length || 0, color: COLORS[1] },
    { name: "Low Value", value: low_value?.length || 0, color: COLORS[2] }
  ];
};

// Error and Loading Components
const ChartErrorState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartEmptyState = ({ message }: { message: string }) => (
  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
    <div className="text-center">
      <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
      <p className="text-sm">{message}</p>
    </div>
  </div>
);

const ChartLoadingState = () => (
  <div className="flex items-center justify-center h-[300px]">
    <div className="space-y-3 w-full">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
      <Skeleton className="h-4 w-2/3" />
      <Skeleton className="h-32 w-full" />
    </div>
  </div>
);

// Helper function to determine chart state
const getChartState = (isLoading: boolean, error: any, hasData: boolean, dataName: string) => {
  if (isLoading) return { type: 'loading' };
  if (error) {
    if (error.message?.includes('404') && error.message?.includes('No data after filtering')) {
      return { type: 'empty', message: `No data after filtering for the selected duration` };
    }
    return { type: 'error', message: `Failed to load ${dataName}` };
  }
  if (!hasData) return { type: 'empty', message: `No ${dataName} available for the selected duration` };
  return { type: 'data' };
};

export default function CustomerSegmentationPage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>('30days');
  const [customStartDate, setCustomStartDate] = useState<Date>();
  const [customEndDate, setCustomEndDate] = useState<Date>();

  if (!user || user.role !== 'merchant_admin') {
    return <div>Access denied. Merchant Admin role required.</div>;
  }

  // Get API parameters based on filter selection
  const apiParams = getApiParamsFromFilter(selectedPeriod, customStartDate, customEndDate);

  // Fetch merchant overview data
  const {
    data: overviewData,
    isLoading,
    error
  } = useMerchantOverview({
    merchantId: user.id,
    topMode: 'amount',
    topLimit: 10,
    ...apiParams
  });

  // Transform API data
  const segmentationData = overviewData ? transformSegmentationData(overviewData.segmentation) : [];

  // Calculate totals for KPIs
  const totalCustomers = segmentationData.reduce((sum, item) => sum + item.value, 0);
  const highValueCustomers = segmentationData.find(item => item.name === "High Value")?.value || 0;
  const midValueCustomers = segmentationData.find(item => item.name === "Mid Value")?.value || 0;
  const lowValueCustomers = segmentationData.find(item => item.name === "Low Value")?.value || 0;

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Customer Segmentation</h2>
        <p className="text-muted-foreground">
          Analyze customer segments based on transaction value and behavior patterns
        </p>
      </div>

      <MerchantFilterBar
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
        customStartDate={customStartDate}
        customEndDate={customEndDate}
        onCustomDateChange={(start, end) => {
          setCustomStartDate(start);
          setCustomEndDate(end);
        }}
        isLoading={isLoading}
      />

      {/* Error Alert */}
      {error && !error.message?.includes('No data after filtering') && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load segmentation data: {error.message}. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      )}

      {/* No Data Alert */}
      {error && error.message?.includes('No data after filtering') && (
        <Alert>
          <Activity className="h-4 w-4" />
          <AlertDescription>
            No customer segmentation data available after filtering for the selected time period. Try selecting a different date range.
          </AlertDescription>
        </Alert>
      )}

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Total Customers"
          value={isLoading ? "..." : error ? "0" : totalCustomers.toString()}
          icon={Users}
          trend={{ value: 12.5, isPositive: true }}
        />
        <KpiCard
          title="High-Value Customers"
          value={isLoading ? "..." : error ? "0" : highValueCustomers.toString()}
          icon={Star}
          trend={{ value: 18.3, isPositive: true }}
        />
        <KpiCard
          title="Mid-Value Customers"
          value={isLoading ? "..." : error ? "0" : midValueCustomers.toString()}
          icon={TrendingUp}
          trend={{ value: 8.7, isPositive: true }}
        />
        <KpiCard
          title="Low-Value Customers"
          value={isLoading ? "..." : error ? "0" : lowValueCustomers.toString()}
          icon={PieChart}
          trend={{ value: 5.2, isPositive: false }}
        />
      </div>

      {/* Main Segmentation Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Customer Segmentation Distribution">
          {(() => {
            const state = getChartState(isLoading, error, segmentationData.length > 0, "customer segmentation data");
            switch (state.type) {
              case 'loading': return <ChartLoadingState />;
              case 'error': return <ChartErrorState message={state.message!} />;
              case 'empty': return <ChartEmptyState message={state.message!} />;
              default: return (
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsPieChart>
                    <Pie
                      data={segmentationData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, value, percent }) => `${name}: ${value} (${(percent * 100).toFixed(1)}%)`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {segmentationData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [value, `${name} Customers`]} />
                    <Legend />
                  </RechartsPieChart>
                </ResponsiveContainer>
              );
            }
          })()}
        </ChartContainer>

        <ChartContainer title="Segment Trends Over Time">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={dummySegmentTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="highValue" stroke={COLORS[0]} strokeWidth={3} name="High Value" />
              <Line type="monotone" dataKey="midValue" stroke={COLORS[1]} strokeWidth={3} name="Mid Value" />
              <Line type="monotone" dataKey="lowValue" stroke={COLORS[2]} strokeWidth={3} name="Low Value" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Value Distribution & Customer Lifetime */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Transaction Value Distribution">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyValueDistribution}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="range" />
              <YAxis />
              <Tooltip formatter={(value, name) => [value, name === "count" ? "Customers" : "Percentage"]} />
              <Bar dataKey="count" fill={COLORS[3]} name="Customer Count" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Customer Lifetime Value by Segment">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyCustomerLifetime}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="segment" />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "avgSpend" ? formatCurrency(Number(value)) :
                name === "retention" ? `${value}%` :
                `${value} months`,
                name === "avgSpend" ? "Avg Spend" :
                name === "retention" ? "Retention Rate" :
                "Avg Lifetime"
              ]} />
              <Bar dataKey="avgSpend" fill={COLORS[4]} name="Avg Spend" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Detailed Segment Analysis */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Segment Performance Overview</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {dummySegmentDetails.map((segment, index) => (
                <div key={segment.segment} className="p-4 border rounded-lg">
                  <div className="flex items-center space-x-2 mb-3">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: segment.color }}
                    ></div>
                    <h3 className="font-semibold">{segment.segment}</h3>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Customers:</span>
                      <span className="font-medium">{segment.customers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Avg Transaction:</span>
                      <span className="font-medium">{formatCurrency(segment.avgTransaction)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Total Spend:</span>
                      <span className="font-medium">{formatCurrency(segment.totalSpend)}</span>
                    </div>
                    
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

    
    </div>
  );
}
