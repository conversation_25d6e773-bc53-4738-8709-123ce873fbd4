"use client";

import { useState } from "react";
import {
  Bar<PERSON>hart3,
  Clock,
  Calendar,
  TrendingUp,
  AlertTriangle,
  Activity,
  Users,
  Target
} from "lucide-react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  ComposedChart,
  Pie<PERSON>hart,
  Pie,
  Cell,
  Legend
} from "recharts";

import { KpiCard } from "@/components/dashboard/KpiCard";
import { ChartContainer } from "@/components/dashboard/ChartContainer";
import { DataTable } from "@/components/dashboard/DataTable";
import { FilterBar, FilterPeriod } from "@/components/dashboard/FilterBar";
import { useAuth } from "@/lib/auth/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils/currency";

const COLORS = ["#1D4987", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#82ca9d"];

// Dummy data for branch-specific peak periods analysis
const dummyBranchHourlyPatterns = [
  { hour: "08:00", transactions: 45, volume: 4500, avgWaitTime: 3.2, tellerUtilization: 85 },
  { hour: "09:00", transactions: 85, volume: 8500, avgWaitTime: 5.8, tellerUtilization: 95 },
  { hour: "10:00", transactions: 120, volume: 12000, avgWaitTime: 8.2, tellerUtilization: 98 },
  { hour: "11:00", transactions: 145, volume: 14500, avgWaitTime: 12.5, tellerUtilization: 100 },
  { hour: "12:00", transactions: 165, volume: 16500, avgWaitTime: 15.8, tellerUtilization: 100 },
  { hour: "13:00", transactions: 155, volume: 15500, avgWaitTime: 14.2, tellerUtilization: 98 },
  { hour: "14:00", transactions: 135, volume: 13500, avgWaitTime: 10.5, tellerUtilization: 92 },
  { hour: "15:00", transactions: 115, volume: 11500, avgWaitTime: 7.8, tellerUtilization: 88 },
  { hour: "16:00", transactions: 95, volume: 9500, avgWaitTime: 5.2, tellerUtilization: 82 },
  { hour: "17:00", transactions: 75, volume: 7500, avgWaitTime: 3.8, tellerUtilization: 75 }
];

const dummyBranchDailyPatterns = [
  { day: "Monday", transactions: 850, volume: 85000, avgValue: 100, peakHour: "12:00", staffingLevel: 6 },
  { day: "Tuesday", transactions: 920, volume: 92000, avgValue: 100, peakHour: "11:00", staffingLevel: 6 },
  { day: "Wednesday", transactions: 880, volume: 88000, avgValue: 100, peakHour: "12:00", staffingLevel: 6 },
  { day: "Thursday", transactions: 950, volume: 95000, avgValue: 100, peakHour: "13:00", staffingLevel: 7 },
  { day: "Friday", transactions: 1100, volume: 110000, avgValue: 100, peakHour: "12:00", staffingLevel: 8 },
  { day: "Saturday", transactions: 650, volume: 65000, avgValue: 100, peakHour: "14:00", staffingLevel: 4 },
  { day: "Sunday", transactions: 0, volume: 0, avgValue: 0, peakHour: "Closed", staffingLevel: 0 }
];

const dummyBranchPeakAnalysis = [
  { period: "Morning Rush (9-11 AM)", transactions: 350, percentage: 28, avgWaitTime: 8.8, color: COLORS[0] },
  { period: "Lunch Peak (12-2 PM)", transactions: 455, percentage: 36, avgWaitTime: 13.2, color: COLORS[1] },
  { period: "Afternoon (3-5 PM)", transactions: 285, percentage: 23, avgWaitTime: 5.6, color: COLORS[2] },
  { period: "Early/Late Hours", transactions: 160, percentage: 13, avgWaitTime: 3.5, color: COLORS[3] }
];

const dummyStaffingOptimization = [
  { hour: "08:00", currentStaff: 4, recommendedStaff: 4, utilization: 85, efficiency: 92 },
  { hour: "09:00", currentStaff: 5, recommendedStaff: 6, utilization: 95, efficiency: 88 },
  { hour: "10:00", currentStaff: 6, recommendedStaff: 7, utilization: 98, efficiency: 85 },
  { hour: "11:00", currentStaff: 6, recommendedStaff: 8, utilization: 100, efficiency: 78 },
  { hour: "12:00", currentStaff: 6, recommendedStaff: 8, utilization: 100, efficiency: 75 },
  { hour: "13:00", currentStaff: 6, recommendedStaff: 7, utilization: 98, efficiency: 82 },
  { hour: "14:00", currentStaff: 5, recommendedStaff: 6, utilization: 92, efficiency: 88 },
  { hour: "15:00", currentStaff: 5, recommendedStaff: 5, utilization: 88, efficiency: 90 },
  { hour: "16:00", currentStaff: 4, recommendedStaff: 4, utilization: 82, efficiency: 92 },
  { hour: "17:00", currentStaff: 4, recommendedStaff: 4, utilization: 75, efficiency: 95 }
];

export default function BranchPeakPeriodsPage() {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<FilterPeriod>('30days');

  if (!user || user.role !== 'branch_admin') {
    return <div>Access denied. Branch Admin role required.</div>;
  }

  // Calculate KPIs from branch peak data
  const peakHourTransactions = Math.max(...dummyBranchHourlyPatterns.map(h => h.transactions));
  const avgDailyTransactions = Math.round(dummyBranchDailyPatterns.filter(d => d.transactions > 0).reduce((sum, day) => sum + day.transactions, 0) / 6);
  const peakWaitTime = Math.max(...dummyBranchHourlyPatterns.map(h => h.avgWaitTime));
  const avgUtilization = Math.round(dummyBranchHourlyPatterns.reduce((sum, h) => sum + h.tellerUtilization, 0) / dummyBranchHourlyPatterns.length);

  return (
    <div className="w-full space-y-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">Peak Transaction Periods</h2>
        <p className="text-muted-foreground">
          Identify peak hours and days for this branch to optimize staffing and resource allocation
        </p>
      </div>

      <FilterBar
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
      />

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Peak Hour Transactions"
          value={peakHourTransactions.toString()}
          icon={BarChart3}
          trend={{ value: 12.5, isPositive: true }}
        />
        <KpiCard
          title="Avg Daily Transactions"
          value={avgDailyTransactions.toString()}
          icon={Calendar}
          trend={{ value: 8.3, isPositive: true }}
        />
        <KpiCard
          title="Peak Wait Time"
          value={`${peakWaitTime} min`}
          icon={Clock}
          trend={{ value: 15.2, isPositive: false }}
        />
        <KpiCard
          title="Avg Teller Utilization"
          value={`${avgUtilization}%`}
          icon={Users}
          trend={{ value: 5.7, isPositive: true }}
        />
      </div>

      {/* Branch Peak Analysis Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Hourly Transaction Pattern">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={dummyBranchHourlyPatterns}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="hour" />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "volume" ? formatCurrency(Number(value)) : 
                name === "avgWaitTime" ? `${value} min` : 
                name === "tellerUtilization" ? `${value}%` : value,
                name === "volume" ? "Volume" : 
                name === "avgWaitTime" ? "Avg Wait Time" : 
                name === "tellerUtilization" ? "Teller Utilization" : "Transactions"
              ]} />
              <Bar dataKey="transactions" fill={COLORS[0]} name="Transactions" />
              <Line type="monotone" dataKey="avgWaitTime" stroke={COLORS[1]} strokeWidth={3} name="Avg Wait Time" />
            </ComposedChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Daily Transaction Volume">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={dummyBranchDailyPatterns.filter(d => d.transactions > 0)}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "volume" ? formatCurrency(Number(value)) : value,
                name === "volume" ? "Volume" : "Transactions"
              ]} />
              <Bar dataKey="transactions" fill={COLORS[1]} name="Transactions" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Peak Period Distribution & Staffing Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartContainer title="Peak Period Distribution">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={dummyBranchPeakAnalysis}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ period, percentage }) => `${percentage}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="transactions"
              >
                {dummyBranchPeakAnalysis.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip formatter={(value, name) => [value, "Transactions"]} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer title="Staffing Optimization">
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart data={dummyStaffingOptimization}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="hour" />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                name === "efficiency" || name === "utilization" ? `${value}%` : value,
                name === "currentStaff" ? "Current Staff" : 
                name === "recommendedStaff" ? "Recommended Staff" :
                name === "utilization" ? "Utilization" : "Efficiency"
              ]} />
              <Bar dataKey="currentStaff" fill={COLORS[2]} name="Current Staff" />
              <Bar dataKey="recommendedStaff" fill={COLORS[3]} name="Recommended Staff" />
              <Line type="monotone" dataKey="efficiency" stroke={COLORS[4]} strokeWidth={3} name="Efficiency %" />
            </ComposedChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Daily Patterns Table */}
      <div>
        <DataTable
          title="Daily Peak Analysis"
          columns={[
            { key: "day", title: "Day" },
            { key: "transactions", title: "Transactions" },
            { key: "volume", title: "Volume", render: (value) => formatCurrency(value) },
            { key: "peakHour", title: "Peak Hour" },
            { key: "staffingLevel", title: "Staff Level" },
          ]}
          data={dummyBranchDailyPatterns.filter(d => d.transactions > 0)}
        />
      </div>

      {/* Branch Peak Insights */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <span>Branch Peak Period Optimization</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <h4 className="font-semibold text-blue-600">Staffing Recommendations</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Add 2 additional tellers during 11:00-13:00 peak</li>
                  <li>• Implement flexible break schedules during rush hours</li>
                  <li>• Cross-train staff for multiple service types</li>
                  <li>• Consider part-time staff for Friday peak periods</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-green-600">Service Efficiency</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Implement express lanes for simple transactions</li>
                  <li>• Pre-position cash and supplies before peak hours</li>
                  <li>• Use queue management to reduce wait times</li>
                  <li>• Promote digital banking during busy periods</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-orange-600">Customer Experience</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Display real-time wait times to customers</li>
                  <li>• Offer appointment scheduling for complex services</li>
                  <li>• Provide comfortable seating and amenities</li>
                  <li>• Send peak hour alerts to regular customers</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-purple-600">Operational Planning</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Schedule maintenance during off-peak hours</li>
                  <li>• Adjust cash management for peak periods</li>
                  <li>• Monitor patterns for seasonal adjustments</li>
                  <li>• Regular review of peak hour strategies</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
